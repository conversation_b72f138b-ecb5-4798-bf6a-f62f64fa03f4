import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../core/constants/firebase_collections.dart';
import '../../core/utils/logger_utils.dart';
import '../../data/models/astrology/chart_data.dart';
import '../../data/models/astrology/chart_settings.dart';
import '../../data/models/astrology/chart_type.dart';
import '../../data/models/interpretation/analysis_category.dart';
import '../../data/models/interpretation/interpretation_record.dart';
import '../../data/models/user/birth_data.dart';
import '../../data/services/api/ai_api_service.dart';
import '../../data/services/api/ai_streaming_service.dart';
import '../../data/services/api/ai_usage_stats_service.dart';
import '../../data/services/api/api_cost_calculator_service.dart';
import '../../data/services/api/astrology_service.dart';
import '../../data/services/api/chart_interpretation_service.dart';
import '../../data/services/api/chart_streaming_interpretation_service.dart';
import '../../data/services/api/firebase_ai_usage_service.dart';
import '../../data/services/api/interpretation_guidance_service.dart';
import '../../data/services/api/interpretation_record_service.dart';
import '../../data/services/api/interpretation_share_service.dart';
import '../../data/services/api/payment_service.dart';
import '../../data/services/api/remote_config_service.dart';
import '../../data/services/interpretation_font_size_service.dart';
import '../../presentation/viewmodels/chart_viewmodel.dart';
import '../../shared/utils/user_preferences.dart';
import '../../shared/widgets/astrology_notice_dialog.dart';
import '../../shared/widgets/common/responsive_wrapper.dart';
import '../../shared/widgets/custom_question_fab.dart';
import '../../shared/widgets/streaming_text_display.dart';
import '../themes/app_theme.dart';
import 'chart_page.dart';
import 'interpretation_records_page.dart';
import 'original_content_page.dart';
import 'settings/purchase_interpretation_page.dart';

/// AI 解讀結果頁面
class AIInterpretationResultPage extends StatefulWidget {
  final ChartData chartData;
  final String interpretationTitle;
  final String subtitle;
  final List<String> suggestedQuestions;
  final String? keyPoint;
  final bool autoExecuteFirstQuestion;
  final AnalysisCategory? analysisCategory;
  final BirthData? birthData;
  final bool enableRating; // 是否啟用評價功能

  const AIInterpretationResultPage({
    super.key,
    required this.chartData,
    required this.interpretationTitle,
    required this.subtitle,
    required this.suggestedQuestions,
    this.keyPoint,
    this.autoExecuteFirstQuestion = false,
    this.analysisCategory,
    this.birthData,
    this.enableRating = true,
  });

  @override
  State<AIInterpretationResultPage> createState() =>
      _AIInterpretationResultPageState();
}

class _AIInterpretationResultPageState
    extends State<AIInterpretationResultPage> {
  AIApiResponse? aiApiResponse;
  String? _interpretation;
  bool _isLoading = false;
  String? _errorMessage;
  String _loadingMessage = '正在解讀...'; // 動態loading訊息
  final bool _isMarkdownMode = true; // 預設使用 Markdown 模式
  final TextEditingController _customQuestionController =
      TextEditingController();

  // 用戶模式狀態
  String _userMode = 'starmaster';
  bool _isLoadingMode = true;

  // 評價相關狀態（當 enableRating 為 true 時使用）
  int _rating = 0;
  String _comment = '';
  bool _hasRated = false;
  bool _isSubmittingRating = false;
  bool _isPublicRating = true; // 是否公開評價
  final TextEditingController _commentController = TextEditingController();

  // 快速回饋選項狀態
  final Set<String> _selectedQuickOptions = <String>{};

  // 字體大小設定
  InterpretationFontSize _currentFontSize = InterpretationFontSize.medium;

  // 流式響應相關狀態
  bool _useStreamingMode = true; // 是否使用流式模式

  // API 費用相關狀態
  double? _apiCost; // API 費用
  int? _promptTokens; // 輸入 tokens
  int? _completionTokens; // 輸出 tokens
  int? _cachedTokens; // 快取 tokens
  String? _costCurrency = 'TWD'; // 費用貨幣
  String? _actualModelName; // 實際使用的模型名稱
  bool _showCostDetails = false; // 是否顯示費用詳情
  Stream<AIStreamingResponse>? _streamingResponse;
  String _streamingContent = '';
  bool _isStreamingComplete = false;

  @override
  void initState() {
    super.initState();
    // 載入用戶模式
    _loadUserMode();

    // 載入字體大小設定
    _loadFontSize();

    // 載入流式模式設定
    _loadStreamingMode();

    // 顯示占星注意事項提示視窗
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showAstrologyNoticeDialog();
    });

    // 如果啟用評價功能，載入已有評價
    // if (widget.enableRating) {
    //   _loadExistingRating();
    // }
  }

  @override
  void dispose() {
    _commentController.dispose();
    _customQuestionController.dispose();
    super.dispose();
  }

  /// 載入用戶模式
  Future<void> _loadUserMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userMode = prefs.getString('user_mode') ?? 'starmaster';

      if (mounted) {
        setState(() {
          _userMode = userMode;
          _isLoadingMode = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _userMode = 'starmaster';
          _isLoadingMode = false;
        });
      }
    }
  }

  /// 載入字體大小設定
  Future<void> _loadFontSize() async {
    try {
      final fontSize = await InterpretationFontSizeService.getCurrentFontSize();
      if (mounted) {
        setState(() {
          _currentFontSize = fontSize;
        });
      }
    } catch (e) {
      logger.e('載入字體大小設定失敗: $e');
    }
  }

  /// 載入流式模式設定
  Future<void> _loadStreamingMode() async {
    try {
      final enabled = await UserPreferences.getStreamingMode();
      if (mounted) {
        setState(() {
          _useStreamingMode = enabled;
        });
      }
      logger.d('載入流式模式設定: $enabled');
    } catch (e) {
      logger.e('載入流式模式設定失敗: $e');
      // 使用預設值
      if (mounted) {
        setState(() {
          _useStreamingMode = true;
        });
      }
    }
  }

  /// 設定流式模式
  Future<void> _setStreamingMode(bool enabled) async {
    try {
      await UserPreferences.saveStreamingMode(enabled);
      if (mounted) {
        setState(() {
          _useStreamingMode = enabled;
        });
      }
      logger.d('設定流式模式: $enabled');
    } catch (e) {
      logger.e('設定流式模式失敗: $e');
    }
  }

  /// 變更字體大小
  Future<void> _changeFontSize(InterpretationFontSize fontSize) async {
    try {
      await InterpretationFontSizeService.setFontSize(fontSize);
      if (mounted) {
        setState(() {
          _currentFontSize = fontSize;
        });
      }

      // 顯示提示訊息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('字體大小已設為：${fontSize.displayName}'),
            duration: const Duration(seconds: 1),
            backgroundColor: AppColors.royalIndigo,
          ),
        );
      }
    } catch (e) {
      logger.e('變更字體大小失敗: $e');
    }
  }

  /// 檢查支付狀態並決定是否繼續解讀
  Future<void> _checkPaymentAndProceed() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _loadingMessage = '正在檢查支付狀態...';
    });

    try {
      // 檢查用戶是否有解讀權限（包含 Firebase 同步，不消費次數，只檢查）
      final hasPermission =
          await PaymentService.hasInterpretationPermissionWithSync();

      if (!mounted) return;

      if (!hasPermission) {
        // 沒有權限，清除loading狀態並顯示支付頁面
        setState(() {
          _isLoading = false;
        });
        _showPaymentRequired();
        return;
      }

      // 有權限，繼續執行解讀（解讀時會自動消費次數）
      if (widget.autoExecuteFirstQuestion &&
          widget.suggestedQuestions.isNotEmpty) {
        // 如果需要自動執行第一個問題，延遲執行以確保頁面完全載入
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted) {
              _askCustomQuestion(widget.suggestedQuestions.first);
            }
          });
        });
      } else {
        _getInterpretation();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = '檢查支付狀態時出錯：$e';
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('檢查支付狀態時出錯：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 檢查並消費權限（每次解讀前調用）
  Future<bool> _checkAndConsumePermission() async {
    try {
      // 檢查用戶是否有解讀權限（包含 AI 使用量檢查）
      final hasPermission = await PaymentService.hasInterpretationPermission();

      if (!hasPermission) {
        // 沒有權限，檢查是否為 AI 使用量限制並顯示相應訊息
        await _checkAndShowAIUsageLimitMessage();

        // 顯示支付頁面
        _showPaymentRequired();
        return false;
      }

      if (await PaymentService.enableAIUsageCheck()) {
        return true;
      }

      // 如果是免費用戶，優先使用單次購買，然後使用免費試用
      final isPremium = await PaymentService.isPremiumUser();
      if (!isPremium) {
        // 先嘗試使用單次購買
        final remainingSinglePurchases =
            await PaymentService.getRemainingSinglePurchases();
        if (remainingSinglePurchases > 0) {
          final success = await PaymentService.useSinglePurchaseAttempt();
          if (success) {
            final remaining =
                await PaymentService.getRemainingSinglePurchases();
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('使用單次購買解讀，剩餘 $remaining 次'),
                  backgroundColor: AppColors.solarAmber,
                  duration: const Duration(seconds: 2),
                ),
              );
            }
            return true;
          } else {
            _showPaymentRequired();
            return false;
          }
        } else {
          // 沒有可用次數，顯示購買提示
          _showPaymentRequired();
          return false;
        }
      }

      // 付費用戶，直接允許
      return true;
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('檢查支付狀態時出錯：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return false;
    }
  }

  /// 檢查並顯示 AI 使用量限制訊息
  Future<void> _checkAndShowAIUsageLimitMessage() async {
    try {
      // 檢查是否啟用 AI 使用量檢查
      final enableAIUsageCheck = RemoteConfigService.getBoolValue(
        'enable_ai_usage_check',
        defaultValue: false,
      );

      if (!enableAIUsageCheck) {
        return; // 未啟用檢查，不顯示訊息
      }

      // 獲取當前選擇的 AI Provider
      final currentProvider = await AIApiService.getCurrentProvider();

      // 檢查是否已達每日使用限制
      final isLimitReached =
          await FirebaseAIUsageService.isLimitReached(currentProvider);

      if (isLimitReached) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('⚠️ 今日使用量已達上限，請明日再試'),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 4),
              action: SnackBarAction(
                label: '關閉',
                textColor: Colors.white,
                onPressed: () {
                  // 關閉 SnackBar
                  ScaffoldMessenger.of(context).hideCurrentSnackBar();
                },
              ),
            ),
          );
        }
        return;
      }

      // 檢查預估使用量是否會超過限制
      final estimatedTokens = 2000; // 預估每次解讀使用的 tokens
      final canUse = await FirebaseAIUsageService.canUseProvider(
        provider: currentProvider,
        estimatedTokens: estimatedTokens,
      );

      if (!canUse) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('⚠️ 預估使用量會超過每日限制。'),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      logger.e('檢查 AI 使用量限制訊息失敗: $e');
    }
  }

  /// 顯示支付頁面
  void _showPaymentRequired() async {
    if (!mounted) return;

    // 直接導航到購買解讀次數頁面
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => const PurchaseInterpretationPage(),
      ),
    );

    // 檢查支付結果
    if (result == true) {
      // 支付成功，重新執行解讀
      logger.i('支付成功，重新執行解讀');
      if (mounted) {
        if (widget.autoExecuteFirstQuestion &&
            widget.suggestedQuestions.isNotEmpty) {
          _askCustomQuestion(widget.suggestedQuestions.first);
        } else {
          _getInterpretation();
        }
      }
    } else {
      // 支付失敗或取消，返回上一頁
      logger.i('支付失敗或取消，返回上一頁');
      if (mounted) {
        Navigator.pop(context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _buildAppBar(),
      body: SingleChildScrollView(
        child: ResponsivePageWrapper(
          maxWidth: 1000.0, // AI 解讀頁面可以稍微寬一些
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 用戶資訊區域（如果是個人分析）
              if (widget.birthData != null) ...[
                ResponsiveCardWrapper(
                  maxWidth: 800.0,
                  child: _buildUserInfoSection(),
                ),
                Divider(
                  height: 1,
                  color: Colors.grey[200],
                ),
              ] else ...[
                // 星盤資料卡片（通用解讀）
                ResponsiveCardWrapper(
                  maxWidth: 800.0,
                  child: _buildEnhancedChartDataCard(),
                ),
              ],

              // 解讀結果區域
              ResponsiveCardWrapper(
                maxWidth: 900.0,
                child: _buildAnalysisResultSection(),
              ),

              // 評價區域（如果啟用）
              if (_isLoading == false &&
                  widget.enableRating &&
                  _shouldShowRatingSection()) ...[
                const SizedBox(height: 24),
                ResponsiveCardWrapper(
                  maxWidth: 800.0,
                  child: _buildRatingSection(),
                ),
              ],

              // 底部間距
              const SizedBox(height: 120),
            ],
          ),
        ),
      ),
      floatingActionButton: CustomQuestionFAB(
        chartData: widget.chartData,
        customTitle: _getCustomQuestionTitle(),
      ),
    );
  }

  /// 獲取自定義問題標題
  String _getCustomQuestionTitle() {
    if (_isLoadingMode) return '自定義問題分析';

    if (_userMode == 'starlight') {
      return '我想問的問題';
    }
    return '${widget.chartData.chartType.displayName}客製化問題分析';
  }

  /// 構建 AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        '解讀結果',
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: AppColors.royalIndigo,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        // 字體大小設定按鈕
        if (!_isLoading)
          PopupMenuButton<InterpretationFontSize>(
            icon: const Icon(Icons.text_fields, color: Colors.white),
            tooltip: '字體大小',
            onSelected: _changeFontSize,
            itemBuilder: (context) =>
                InterpretationFontSizeService.getAllFontSizes()
                    .map((fontSize) => PopupMenuItem(
                          value: fontSize,
                          child: Row(
                            children: [
                              Icon(
                                _currentFontSize == fontSize
                                    ? Icons.radio_button_checked
                                    : Icons.radio_button_unchecked,
                                size: 20,
                                color: AppColors.indigoSurface,
                              ),
                              const SizedBox(width: 12),
                              Text(fontSize.displayName),
                            ],
                          ),
                        ))
                    .toList(),
          ),
        // 查看星盤按鈕
        IconButton(
          icon: const Icon(Icons.visibility_outlined, color: Colors.white),
          onPressed: _navigateToChartPage,
          tooltip: '查看星盤',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            // 開發模式：複製 AI 解讀內容按鈕
            if (kDebugMode) ...[
              const PopupMenuItem(
                value: 'copyInterpretation',
                child: Row(
                  children: [
                    Icon(Icons.content_copy_rounded,
                        size: 20, color: AppColors.indigoSurface),
                    SizedBox(width: 12),
                    Text('複製 AI 解讀內容 (開發模式)'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'copy',
                child: Row(
                  children: [
                    Icon(Icons.copy, size: 20, color: AppColors.indigoSurface),
                    SizedBox(width: 12),
                    Text('複製解讀'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'viewOriginal',
                child: Row(
                  children: [
                    Icon(Icons.code, size: 20, color: AppColors.indigoSurface),
                    SizedBox(width: 12),
                    Text('查看原始內容'),
                  ],
                ),
              ),
              // 流式模式切換
              PopupMenuItem(
                value: 'toggleStreaming',
                child: Row(
                  children: [
                    Icon(
                      _useStreamingMode ? Icons.flash_on : Icons.flash_off,
                      size: 20,
                      color: AppColors.indigoSurface,
                    ),
                    const SizedBox(width: 12),
                    Text(_useStreamingMode ? '關閉即時顯示' : '開啟即時顯示'),
                  ],
                ),
              ),
            ],
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share, size: 20, color: AppColors.indigoSurface),
                  SizedBox(width: 12),
                  Text('分享解讀'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'history',
              child: Row(
                children: [
                  Icon(Icons.history, size: 20, color: AppColors.indigoSurface),
                  SizedBox(width: 12),
                  Text('解讀紀錄'),
                ],
              ),
            ),
            // API 費用相關選項
            if (_apiCost != null) ...[
              PopupMenuItem(
                value: 'toggleCurrency',
                child: Row(
                  children: [
                    const Icon(Icons.currency_exchange, size: 20, color: AppColors.indigoSurface),
                    const SizedBox(width: 12),
                    Text('切換貨幣 (${_costCurrency == 'TWD' ? 'USD' : 'TWD'})'),
                  ],
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  /// 處理菜單操作
  void _handleMenuAction(String action) {
    switch (action) {
      case 'copyInterpretation':
        _copyInterpretation();
        break;
      case 'copy':
        _copyInterpretationResult();
        break;
      case 'viewOriginal':
        _viewOriginalContent();
        break;
      case 'share':
        _shareInterpretation();
        break;
      case 'history':
        _navigateToRecords();
        break;
      case 'toggleStreaming':
        _toggleStreamingMode();
        break;
      case 'toggleCurrency':
        _toggleCostCurrency();
        break;
    }
  }

  /// 切換流式模式
  void _toggleStreamingMode() {
    final newMode = !_useStreamingMode;
    _setStreamingMode(newMode);

    // 顯示提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          newMode ? '已開啟即時顯示模式' : '已關閉即時顯示模式',
        ),
        duration: const Duration(seconds: 2),
        backgroundColor: AppColors.royalIndigo,
      ),
    );
  }

  /// 構建用戶資訊區域
  Widget _buildUserInfoSection() {
    if (widget.birthData == null) return const SizedBox.shrink();

    final birthData = widget.birthData!;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: AppColors.royalIndigo.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Icon(
                  Icons.person,
                  color: AppColors.royalIndigo,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      birthData.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.normal,
                        color: AppColors.textDark,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.analysisCategory?.name ?? widget.subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // 出生資訊
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Column(
              children: [
                _buildInfoRow('出生日期',
                    '${birthData.dateTime.year}年${birthData.dateTime.month}月${birthData.dateTime.day}日'),
                const SizedBox(height: 8),
                _buildTimeInfoRow(birthData),
                const SizedBox(height: 8),
                _buildInfoRow('出生地點', birthData.birthPlace),
              ],
            ),
          ),

          // 出生時間不確定警告
          if (birthData.isTimeUncertain) ...[
            const SizedBox(height: 16),
            _buildTimeUncertaintyWarning(),
          ],
        ],
      ),
    );
  }

  /// 構建資訊行
  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 70,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey[600],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 13,
              color: AppColors.textDark,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  /// 構建分析結果區域
  Widget _buildAnalysisResultSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_isLoading) ...[
            _buildLoadingState(),
          ] else if (_useStreamingMode && _streamingResponse != null) ...[
            // 流式模式：顯示流式內容
            _buildInterpretationContent(),
          ] else if (_interpretation?.isNotEmpty ?? false) ...[
            // 傳統模式：顯示完整內容
            _buildInterpretationContent(),
          ] else ...[
            _buildEmptyState(),
          ],
        ],
      ),
    );
  }

  /// 構建載入狀態
  Widget _buildLoadingState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(40),
      child: Column(
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.royalIndigo),
          ),
          const SizedBox(height: 16),
          Text(
            _loadingMessage,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '請稍候，這可能需要一段時間',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建空狀態
  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(40),
      child: Column(
        children: [
          Icon(
            Icons.psychology,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '沒有解讀內容',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '請重新開始分析',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建美化的星盤資料卡片
  Widget _buildEnhancedChartDataCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 星盤類型標題區域
          _buildChartTypeHeader(),

          const SizedBox(height: 16),

          // 星盤資訊容器
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 主要人物信息
                _buildPrimaryPersonInfo(),

                // 第二個人信息（如果有）
                if (widget.chartData.secondaryPerson != null) ...[
                  const SizedBox(height: 12),
                  _buildSecondaryPersonInfo(),
                ],

                // 特殊日期信息（如果有）
                if (widget.chartData.specificDate != null) ...[
                  const SizedBox(height: 12),
                  _buildSpecificDateInfo(),
                ],

                if (widget.chartData.returnDate != null) ...[
                  const SizedBox(height: 12),
                  _buildReturnDateInfo(),
                ],

                // 出生時間不確定警告
                if (_hasTimeUncertainty()) ...[
                  const SizedBox(height: 12),
                  _buildChartTimeUncertaintyWarning(),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建星盤類型標題區域
  Widget _buildChartTypeHeader() {
    if (_isLoadingMode) {
      return _buildLoadingHeader();
    }

    final chartType = widget.chartData.chartType;
    final categoryColor = _getChartTypeCategoryColor(chartType);

    return Row(
      children: [
        // 星盤類型圖標
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: categoryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(25),
          ),
          child: Icon(
            _getChartTypeIcon(chartType),
            color: categoryColor,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getDisplayTitle(chartType),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.normal,
                  color: AppColors.textDark,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                _getDisplayDescription(chartType),
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[600],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                widget.interpretationTitle,
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[600],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                widget.subtitle,
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[600],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 構建載入中的標題
  Widget _buildLoadingHeader() {
    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: Colors.grey.shade300,
            borderRadius: BorderRadius.circular(14),
          ),
          child: const Icon(
            Icons.hourglass_empty,
            color: Colors.grey,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 20,
                width: 150,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(height: 8),
              Container(
                height: 14,
                width: 200,
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 構建主要人物信息
  Widget _buildPrimaryPersonInfo() {
    final person = widget.chartData.primaryPerson;
    String personInfo = person.name;

    if (person.isTimeUncertain) {
      personInfo += ' (時間不確定)';
    }

    return _buildInfoRow('主要人物', personInfo);
  }

  /// 構建第二個人信息
  Widget _buildSecondaryPersonInfo() {
    final person = widget.chartData.secondaryPerson!;
    String personInfo = person.name;

    if (person.isTimeUncertain) {
      personInfo += ' (時間不確定)';
    }

    return _buildInfoRow('次要人物', personInfo);
  }

  /// 構建特殊日期信息
  Widget _buildSpecificDateInfo() {
    final specificDate = widget.chartData.specificDate!;

    return _buildInfoRow(
        _getSpecificDateLabel(), _formatDateTime(specificDate));
  }

  /// 構建特殊日期信息
  Widget _buildReturnDateInfo() {
    final returnDate = widget.chartData.returnDate!;

    return _buildInfoRow('返照時間', _formatDateTime(returnDate));
  }

  /// 構建解讀內容
  Widget _buildInterpretationContent() {
    // logger.d(
    //     '構建解讀內容 - 流式模式: $_useStreamingMode, 流式響應: ${_streamingResponse != null}, 載入中: $_isLoading');

    // 如果使用流式模式且有流式響應
    if (_useStreamingMode && _streamingResponse != null && kDebugMode) {
      // logger.d('使用流式內容顯示');
      return _buildStreamingInterpretationContent();
    }

    // 傳統模式的處理
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.royalIndigo),
            ),
            const SizedBox(height: 16),
            Text(
              _loadingMessage,
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.royalIndigo,
              ),
            ),
          ],
        ),
      );
    }

    if (aiApiResponse?.success == false) {
      return _buildErrorContent();
    }

    if (_interpretation == null) {
      return const Center(
        child: Text(
          '暫無解讀內容',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 解讀內容
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
          child: _buildInterpretationContentDisplay(),
        ),
        // API 費用顯示
        if (_apiCost != null) _buildAPICostDisplay(),
      ],
    );
  }

  /// 構建流式解讀內容
  Widget _buildStreamingInterpretationContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
          child: StreamingTextDisplay(
        stream: _streamingResponse!,
        textStyle: TextStyle(
          fontSize: InterpretationFontSizeService.getParagraphFontSize(
              _currentFontSize),
          height: InterpretationFontSizeService.getLineHeight(_currentFontSize),
          color: AppColors.textDark,
        ),
        enableMarkdown: _isMarkdownMode,
        onComplete: (content, {AIStreamingResponse? finalResponse}) {
          setState(() {
            _isStreamingComplete = true;
            _streamingContent = content;
          });
          // logger.d('流式響應完成，內容長度: ${content.length}');
          // 保存解讀紀錄
          if (content.isNotEmpty) {
            _saveInterpretationRecord(content);
          }
          // 計算 API 費用，使用實際的 token 使用量
          Map<String, dynamic>? actualUsage;
          if (finalResponse != null) {
            actualUsage = {
              'prompt_tokens': finalResponse.promptTokens,
              'completion_tokens': finalResponse.completionTokens,
              'total_tokens': finalResponse.totalTokens,
              'model_name': finalResponse.modelName,
            };
          }
          _calculateAndDisplayAPICost(content, actualUsage: actualUsage);
        },
        onError: (error) {
          setState(() {
            _errorMessage = error;
          });
        },
        onStatusUpdate: (status) {
          setState(() {
            _loadingMessage = status;
          });
        },
        minHeight: 200,
      ),
    ),
        // API 費用顯示（流式模式）
        if (_isStreamingComplete && _apiCost != null) _buildAPICostDisplay(),
      ],
    );
  }

  /// 構建錯誤內容
  Widget _buildErrorContent() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              '解讀失敗',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.normal,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              aiApiResponse!.content,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _getInterpretation,
              icon: const Icon(Icons.refresh),
              label: const Text('重試'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.royalIndigo,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 獲取 AI 解讀
  Future<void> _getInterpretation() async {
    // 檢查權限
    if (!await _checkAndConsumePermission()) {
      return;
    }

    if (_useStreamingMode) {
      // 使用流式響應
      logger.i('使用流式響應模式');
      _getStreamingInterpretation();
    } else {
      // 使用傳統響應
      logger.i('使用傳統響應模式');
      _getTraditionalInterpretation();
    }
  }

  /// 獲取流式 AI 解讀
  Future<void> _getStreamingInterpretation() async {
    logger.i('開始獲取流式 AI 解讀');

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _loadingMessage = '正在準備解讀...';
      _streamingContent = '';
      _isStreamingComplete = false;
      _interpretation = null;
    });

    try {
      logger.d(
          '調用 ChartStreamingInterpretationService.getStreamingChartInterpretation');
      _streamingResponse =
          ChartStreamingInterpretationService.getStreamingChartInterpretation(
        widget.chartData,
        widget.interpretationTitle,
        widget.subtitle,
        keyPoint: widget.keyPoint,
        suggestedQuestions: widget.suggestedQuestions,
      );

      setState(() {
        _isLoading = false; // 流式響應開始後就不再顯示傳統的 loading
      });

      logger.d('流式響應已設置，開始顯示流式內容');
    } catch (e) {
      logger.e('獲取流式解讀失敗: $e');
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  /// 獲取傳統 AI 解讀
  Future<void> _getTraditionalInterpretation() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _loadingMessage = '正在解讀...';
    });

    try {
      aiApiResponse = await ChartInterpretationService.getChartInterpretation(
          widget.chartData, widget.interpretationTitle, widget.subtitle,
          keyPoint: widget.keyPoint);

      setState(() async {
        if (aiApiResponse?.success == false) {
          _errorMessage = aiApiResponse?.content;
          _interpretation = '解讀失敗：${aiApiResponse?.content}';
        } else {
          _interpretation = aiApiResponse?.content;
          // 保存解讀紀錄
          await _saveInterpretationRecord(aiApiResponse!.content);
          // 計算 API 費用，使用實際的 token 使用量
          Map<String, dynamic>? actualUsage;
          if (aiApiResponse!.rawUsage != null) {
            actualUsage = Map<String, dynamic>.from(aiApiResponse!.rawUsage!);
            // 添加模型名稱
            if (aiApiResponse!.modelName != null) {
              actualUsage['model'] = aiApiResponse!.modelName;
            }
          } else {
            // 回退到基本資訊
            actualUsage = {
              'prompt_tokens': aiApiResponse!.promptTokens,
              'completion_tokens': aiApiResponse!.completionTokens,
              'total_tokens': aiApiResponse!.totalTokens,
              'model_name': aiApiResponse!.modelName,
              if (aiApiResponse!.modelName != null) 'model_name': aiApiResponse!.modelName,
            };
          }
          _calculateAndDisplayAPICost(aiApiResponse!.content, actualUsage: actualUsage);
        }
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  /// 詢問自訂問題
  Future<void> _askCustomQuestion(String question) async {
    // 檢查權限
    if (!await _checkAndConsumePermission()) {
      return;
    }

    if (_useStreamingMode) {
      // 使用流式響應
      _askStreamingCustomQuestion(question);
    } else {
      // 使用傳統響應
      _askTraditionalCustomQuestion(question);
    }
  }

  /// 詢問流式自訂問題
  Future<void> _askStreamingCustomQuestion(String question) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _loadingMessage = '正在分析您的問題...';
      _streamingContent = '';
      _isStreamingComplete = false;
      _interpretation = null;
    });

    try {
      _streamingResponse =
          ChartStreamingInterpretationService.getStreamingCustomInterpretation(
        chartData: widget.chartData,
        customPrompt: '''
請根據星盤資料回答這個問題：
$question
''',
      );

      setState(() {
        _isLoading = false; // 流式響應開始後就不再顯示傳統的 loading
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  /// 詢問傳統自訂問題
  Future<void> _askTraditionalCustomQuestion(String question) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _loadingMessage = '正在分析您的問題...';
    });

    try {
      aiApiResponse = await ChartInterpretationService.getCustomInterpretation(
        chartData: widget.chartData,
        customPrompt: '''
請根據星盤資料回答這個問題：
$question
''',
      );

      setState(() async {
        if (aiApiResponse?.success == false) {
          _errorMessage = aiApiResponse?.content;
          _interpretation = '解讀失敗：${aiApiResponse?.content}';
        } else {
          _interpretation = aiApiResponse?.content;
          // 保存解讀紀錄
          // 保存自訂問題的解讀紀錄
          await _saveInterpretationRecord(aiApiResponse!.content,
              customQuestion: question);
        }
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  /// 複製解讀內容
  Future<void> _copyInterpretation() async {
    try {
      // 檢查星盤數據是否完整，如果不完整則重新計算
      ChartData chartData = widget.chartData;
      if (chartData.planets == null || chartData.houses == null) {
        logger.w('數據為空，重新計算');
        // 重新計算
        ChartSettings chartSettings = await ChartSettings.loadFromPrefs();
        chartData = await AstrologyService()
            .calculateChartData(chartData, chartSettings: chartSettings);
      }

      // 構建星盤數據摘要
      String chartSummary = await AIApiService.buildChartSummary(chartData);
      String guidance = await InterpretationGuidanceService.getGuidance();
      String prompt = await ChartInterpretationService.getPrompt(
        chartData,
        widget.interpretationTitle,
        widget.subtitle,
        keyPoint: widget.keyPoint,
      );

      // 構建完整的複製內容，包含解讀內容和星盤摘要
      final buffer = StringBuffer();

      buffer.writeln(chartSummary);
      buffer.writeln();
      buffer.writeln(prompt);
      buffer.writeln();
      buffer.writeln(guidance);

      final fullContent = buffer.toString();

      await Clipboard.setData(ClipboardData(text: fullContent));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('解讀內容和星盤摘要已複製到剪貼板'),
            backgroundColor: AppColors.royalIndigo,
          ),
        );
      }
    } catch (e) {
      logger.e('複製解讀內容時出錯: $e');

      // 如果出錯，則只複製解讀內容
      await Clipboard.setData(ClipboardData(text: _interpretation!));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('解讀內容已複製到剪貼板'),
            backgroundColor: AppColors.royalIndigo,
          ),
        );
      }
    }
  }

  /// 複製解讀內容
  Future<void> _copyInterpretationResult() async {
    // 獲取當前的解讀內容（支援流式和傳統模式）
    String? contentToCopy;

    if (_useStreamingMode && _streamingContent.isNotEmpty) {
      // 流式模式：使用 _streamingContent
      contentToCopy = _streamingContent;
      logger.d('使用流式內容進行複製，長度: ${contentToCopy.length}');
    } else if (_interpretation != null && _interpretation!.isNotEmpty) {
      // 傳統模式：使用 _interpretation
      contentToCopy = _interpretation;
      logger.d('使用傳統內容進行複製，長度: ${contentToCopy?.length}');
    }

    if (contentToCopy != null && contentToCopy.isNotEmpty) {
      try {
        // 移除 Markdown 格式，方便分享給其他人
        // final cleanContent = _removeMarkdownFormatting(contentToCopy);

        await Clipboard.setData(ClipboardData(text: contentToCopy));

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('解讀內容已複製到剪貼板（已移除格式）'),
              backgroundColor: AppColors.royalIndigo,
            ),
          );
        }
        logger.d('解讀內容複製成功');
      } catch (e) {
        logger.e('複製解讀內容時出錯: $e');

        // 如果出錯，則只複製解讀內容（也移除格式）
        final cleanContent = _removeMarkdownFormatting(contentToCopy);
        await Clipboard.setData(ClipboardData(text: cleanContent));

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('解讀內容已複製到剪貼板'),
              backgroundColor: AppColors.royalIndigo,
            ),
          );
        }
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('目前沒有可複製的解讀內容'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      logger.w(
          '沒有可複製的內容 - 流式模式: $_useStreamingMode, 流式內容長度: ${_streamingContent.length}, 傳統內容長度: ${_interpretation?.length ?? 0}');
    }
  }

  /// 分享解讀內容
  Future<void> _shareInterpretation() async {
    // 獲取當前的解讀內容（支援流式和傳統模式）
    String? contentToShare;

    if (_useStreamingMode && _streamingContent.isNotEmpty) {
      // 流式模式：使用 _streamingContent
      contentToShare = _streamingContent;
      logger.d('使用流式內容進行分享，長度: ${contentToShare.length}');
    } else if (_interpretation != null && _interpretation!.isNotEmpty) {
      // 傳統模式：使用 _interpretation
      contentToShare = _interpretation;
      logger.d('使用傳統內容進行分享，長度: ${contentToShare?.length}');
    }

    if (contentToShare != null && contentToShare.isNotEmpty) {
      // 獲取人物姓名
      String? primaryPersonName;
      String? secondaryPersonName;

      if (widget.chartData.primaryPerson.name.isNotEmpty) {
        primaryPersonName = widget.chartData.primaryPerson.name;
      }

      if (widget.chartData.secondaryPerson?.name.isNotEmpty == true) {
        secondaryPersonName = widget.chartData.secondaryPerson!.name;
      }

      // 使用共用的分享服務
      await InterpretationShareService.shareInterpretation(
        context: context,
        interpretation: contentToShare,
        title: widget.interpretationTitle,
        chartData: widget.chartData,
        personName: primaryPersonName,
        secondaryPersonName: secondaryPersonName,
        interpretationType: widget.subtitle,
      );
      logger.d('解讀內容分享成功');
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('目前沒有可分享的解讀內容'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      logger.w(
          '沒有可分享的內容 - 流式模式: $_useStreamingMode, 流式內容長度: ${_streamingContent.length}, 傳統內容長度: ${_interpretation?.length ?? 0}');
    }
  }

  /// 查看原始內容
  void _viewOriginalContent() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OriginalContentPage(
          chartData: widget.chartData,
          interpretationTitle: widget.interpretationTitle,
          subtitle: widget.subtitle,
          keyPoint: widget.keyPoint,
        ),
      ),
    );
  }

  /// 構建 API 費用顯示組件
  Widget _buildAPICostDisplay() {
    if (_apiCost == null) return const SizedBox.shrink();
    if(kReleaseMode) return const SizedBox.shrink();

    const SizedBox(height: 40);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.calculate_outlined,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'API 費用',
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[700],
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Text(
                    APICostCalculatorService.formatCost(
                      _apiCost!,
                      currency: _costCurrency ?? 'TWD',
                    ),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _apiCost! == 0.0 ? Colors.green : AppColors.royalIndigo,
                    ),
                  ),
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _showCostDetails = !_showCostDetails;
                      });
                    },
                    child: Icon(
                      _showCostDetails ? Icons.expand_less : Icons.expand_more,
                      size: 18,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
          if (_showCostDetails) ...[
            const SizedBox(height: 8),
            Divider(color: Colors.grey[300], height: 1),
            const SizedBox(height: 8),
            _buildCostDetailRow('輸入 Tokens', '${_promptTokens ?? 0}'),
            if (_cachedTokens != null && _cachedTokens! > 0)
              _buildCostDetailRow('快取 Tokens', '$_cachedTokens'),
            _buildCostDetailRow('輸出 Tokens', '${_completionTokens ?? 0}'),
            _buildCostDetailRow('總計 Tokens', '${(_promptTokens ?? 0) + (_completionTokens ?? 0)}'),
            const SizedBox(height: 4),
            _buildProviderInfo(),
            // _buildCostSavingTip(),
          ],
        ],
      ),
    );
  }

  /// 構建費用詳情行
  Widget _buildCostDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建提供商資訊
  Widget _buildProviderInfo() {
    return FutureBuilder<AIProvider>(
      future: AIApiService.getCurrentProvider(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const SizedBox.shrink();

        final provider = snapshot.data!;
        // 使用實際的模型名稱獲取定價
        final pricing = APICostCalculatorService.getPricing(provider, modelName: _actualModelName);

        return Container(
          margin: const EdgeInsets.only(top: 6),
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    AIUsageStatsService.getProviderIcon(provider),
                    style: const TextStyle(fontSize: 14),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    provider.displayName,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (_actualModelName != null) ...[
                    const SizedBox(width: 6),
                    Text(
                      '($_actualModelName)',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey[600],
                      ),
                    ),
                  ] else if (pricing != null) ...[
                    const SizedBox(width: 6),
                    Text(
                      '(${pricing.modelName})',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ],
              ),
              if (pricing != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      '輸入: \$${pricing.inputCostPerM}/1M',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '輸出: \$${pricing.outputCostPerM}/1M',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
                if (pricing.supportsCachedInput) ...[
                  const SizedBox(height: 2),
                  Text(
                    '快取: \$${pricing.cachedInputCostPerM}/1M',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.green[600],
                    ),
                  ),
                ],
              ],
            ],
          ),
        );
      },
    );
  }

  /// 構建費用節省建議
  Widget _buildCostSavingTip() {
    if (_promptTokens == null || _completionTokens == null) {
      return const SizedBox.shrink();
    }

    return FutureBuilder<AIProvider>(
      future: AIApiService.getCurrentProvider(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const SizedBox.shrink();

        final currentProvider = snapshot.data!;
        final tip = APICostCalculatorService.getCostSavingTip(
          currentProvider,
          _promptTokens!,
          _completionTokens!,
          currentModelName: _actualModelName,
        );

        if (tip.isEmpty) return const SizedBox.shrink();

        return Container(
          margin: const EdgeInsets.only(top: 6),
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.amber[50],
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: Colors.amber[200]!),
          ),
          child: Text(
            tip,
            style: TextStyle(
              fontSize: 11,
              color: Colors.amber[800],
            ),
          ),
        );
      },
    );
  }

  /// 切換費用顯示貨幣
  void _toggleCostCurrency() {
    setState(() {
      _costCurrency = _costCurrency == 'TWD' ? 'USD' : 'TWD';
    });

    // 重新計算費用
    if (_promptTokens != null && _completionTokens != null) {
      _recalculateAPICost();
    }

    // 顯示提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('費用顯示已切換為 ${_costCurrency == 'TWD' ? '台幣' : '美元'}'),
        backgroundColor: AppColors.royalIndigo,
        duration: const Duration(seconds: 1),
      ),
    );
  }

  /// 重新計算 API 費用（當切換貨幣時）
  Future<void> _recalculateAPICost() async {
    if (_promptTokens == null || _completionTokens == null) return;

    try {
      final currentProvider = await AIApiService.getCurrentProvider();

      double cost;
      if (_actualModelName != null && currentProvider == AIProvider.openai) {
        // 使用具體模型計算費用
        cost = APICostCalculatorService.calculateModelAPICost(
          provider: currentProvider,
          modelName: _actualModelName!,
          promptTokens: _promptTokens!,
          completionTokens: _completionTokens!,
          cachedTokens: _cachedTokens ?? 0,
          currency: _costCurrency ?? 'TWD',
        );
      } else {
        // 使用預設提供商計算費用
        cost = APICostCalculatorService.calculateAPICost(
          provider: currentProvider,
          promptTokens: _promptTokens!,
          completionTokens: _completionTokens!,
          currency: _costCurrency ?? 'TWD',
        );
      }

      if (mounted) {
        setState(() {
          _apiCost = cost;
        });
      }
    } catch (e) {
      logger.e('重新計算 API 費用失敗: $e');
    }
  }

  /// 計算並顯示 API 費用
  ///
  /// [responseContent] AI 回應內容
  /// [actualUsage] 實際的 token 使用量資訊（可選）
  Future<void> _calculateAndDisplayAPICost(
    String responseContent, {
    Map<String, dynamic>? actualUsage,
  }) async {
    try {
      // 獲取當前使用的 AI 提供商
      final currentProvider = await AIApiService.getCurrentProvider();

      int promptTokens;
      int completionTokens;
      int cachedTokens = 0;
      String? actualModelName;

      // 優先使用實際的 token 使用量
      if (actualUsage != null && actualUsage['prompt_tokens'] != null && actualUsage['completion_tokens'] != null) {
        promptTokens = actualUsage['prompt_tokens'] as int? ?? 0;
        completionTokens = actualUsage['completion_tokens'] as int? ?? 0;

        // 檢查是否有快取 tokens
        final promptDetails = actualUsage['prompt_tokens_details'] as Map<String, dynamic>?;
        if (promptDetails != null) {
          cachedTokens = promptDetails['cached_tokens'] as int? ?? 0;
        }

        // 提取模型名稱
        actualModelName = actualUsage['model_name'] as String?;

        logger.i('使用實際 token 使用量 - 輸入: $promptTokens, 輸出: $completionTokens, 快取: $cachedTokens, 模型: $actualModelName');
      } else {
        // 回退到估算方式
        logger.w('未提供實際 token 使用量，使用估算方式');

        String promptText = '';
        try {
          // 重新構建提示詞以獲得準確的 token 估算
          final chartSummary = await AIApiService.buildChartSummary(widget.chartData);
          final fullPrompt = await AIApiService.buildFullPrompt(
            await ChartInterpretationService.getPrompt(
              widget.chartData,
              widget.interpretationTitle,
              widget.subtitle,
              keyPoint: widget.keyPoint,
            ),
            chartSummary,
          );
          promptText = fullPrompt;
        } catch (e) {
          logger.w('無法重新構建提示詞，使用簡化估算: $e');
          // 使用簡化的提示詞估算
          promptText = '${widget.interpretationTitle} ${widget.subtitle} ${widget.keyPoint ?? ''}';
        }

        // 估算 token 數量
        promptTokens = APICostCalculatorService.estimateTokens(promptText);
        completionTokens = APICostCalculatorService.estimateTokens(responseContent);
        // 提取模型名稱
        actualModelName = actualUsage?['model_name'] as String?;
      }

      // 計算費用
      double cost;
      if (actualModelName != null && currentProvider == AIProvider.openai) {
        // 使用具體模型計算費用
        cost = APICostCalculatorService.calculateModelAPICost(
          provider: currentProvider,
          modelName: actualModelName,
          promptTokens: promptTokens,
          completionTokens: completionTokens,
          cachedTokens: cachedTokens,
          currency: _costCurrency ?? 'TWD',
        );
      } else {
        // 使用預設提供商計算費用
        cost = APICostCalculatorService.calculateAPICost(
          provider: currentProvider,
          promptTokens: promptTokens,
          completionTokens: completionTokens,
          modelName: actualModelName,
          currency: _costCurrency ?? 'TWD',
        );
      }

      if (mounted) {
        setState(() {
          _apiCost = cost;
          _promptTokens = promptTokens;
          _completionTokens = completionTokens;
          _actualModelName = actualModelName;
          _cachedTokens = cachedTokens;
        });
      }

      logger.i('API 費用計算完成: ${APICostCalculatorService.formatCost(cost, currency: _costCurrency ?? 'TWD')}');
      logger.d('Token 使用量 - 輸入: $promptTokens, 輸出: $completionTokens, 快取: $cachedTokens, 總計: ${promptTokens + completionTokens}');

    } catch (e) {
      logger.e('計算 API 費用失敗: $e');
    }
  }

  /// 保存解讀紀錄
  Future<void> _saveInterpretationRecord(String interpretation,
      {String? customQuestion}) async {
    try {
      final recordId = '${DateTime.now().millisecondsSinceEpoch}';

      final record = InterpretationRecord.fromChartData(
        id: recordId,
        title: widget.interpretationTitle,
        interpretationType: widget.chartData.chartType.name,
        content: interpretation,
        chartData: widget.chartData,
        tags: customQuestion != null ? ['自訂問題'] : [],
      );

      await InterpretationRecordService.saveRecord(record);
    } catch (e) {
      // 保存失敗不影響用戶體驗，只記錄錯誤
      debugPrint('保存解讀紀錄失敗: $e');
    }
  }

  /// 構建解讀內容顯示
  Widget _buildInterpretationContentDisplay() {
    if (_isMarkdownMode) {
      return MarkdownBody(
        data: _interpretation!,
        styleSheet: MarkdownStyleSheet(
          p: TextStyle(
            fontSize: InterpretationFontSizeService.getParagraphFontSize(
                _currentFontSize),
            height:
                InterpretationFontSizeService.getLineHeight(_currentFontSize),
            color: AppColors.textDark,
          ),
          h1: TextStyle(
            fontSize:
                InterpretationFontSizeService.getH1FontSize(_currentFontSize),
            fontWeight: FontWeight.normal,
            color: AppColors.royalIndigo,
            height: 1.4,
          ),
          h2: TextStyle(
            fontSize:
                InterpretationFontSizeService.getH2FontSize(_currentFontSize),
            fontWeight: FontWeight.normal,
            color: AppColors.royalIndigo,
            height: 1.4,
          ),
          h3: TextStyle(
            fontSize:
                InterpretationFontSizeService.getH3FontSize(_currentFontSize),
            fontWeight: FontWeight.normal,
            color: AppColors.textDark,
            height: 1.4,
          ),
          strong: TextStyle(
            fontSize: InterpretationFontSizeService.getParagraphFontSize(
                _currentFontSize),
            fontWeight: FontWeight.normal,
            color: AppColors.textDark,
          ),
          em: TextStyle(
            fontSize: InterpretationFontSizeService.getParagraphFontSize(
                _currentFontSize),
            fontStyle: FontStyle.italic,
            color: AppColors.textDark,
          ),
          blockquote: TextStyle(
            fontSize: InterpretationFontSizeService.getBlockquoteFontSize(
                _currentFontSize),
            fontStyle: FontStyle.italic,
            color: Colors.grey.shade600,
            backgroundColor: Colors.grey.shade100,
          ),
          code: TextStyle(
            fontSize:
                InterpretationFontSizeService.getCodeFontSize(_currentFontSize),
            fontFamily: 'monospace',
            backgroundColor: Colors.grey.shade200,
            color: AppColors.textDark,
          ),
          listBullet: TextStyle(
            fontSize: InterpretationFontSizeService.getListBulletFontSize(
                _currentFontSize),
            color: AppColors.royalIndigo,
          ),
        ),
        onTapLink: (text, href, title) {
          if (href != null) {
            _launchUrl(href);
          }
        },
      );
    } else {
      return Text(
        _interpretation!,
        style: TextStyle(
          fontSize: InterpretationFontSizeService.getParagraphFontSize(
              _currentFontSize),
          height: InterpretationFontSizeService.getLineHeight(_currentFontSize),
          color: AppColors.textDark,
        ),
      );
    }
  }

  /// 啟動 URL
  Future<void> _launchUrl(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('無法打開連結: $url')),
        );
      }
    }
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 '
        '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 獲取特殊日期標籤
  String _getSpecificDateLabel() {
    final chartType = widget.chartData.chartType;

    if (chartType.isPredictiveChart ||
        chartType.isRelationshipPredictiveChart) {
      return '推運時間';
    } else if (chartType.isReturnChart) {
      return '推運時間';
    } else if (chartType == ChartType.eclipse) {
      return '蝕相時間';
    } else if (chartType == ChartType.equinoxSolstice) {
      return '節氣時間';
    } else {
      return '分析時間';
    }
  }

  /// 獲取星盤類型圖標
  IconData _getChartTypeIcon(ChartType chartType) {
    switch (chartType) {
      case ChartType.natal:
        return Icons.person_rounded;
      case ChartType.transit:
        return Icons.timeline_rounded;
      case ChartType.synastry:
        return Icons.favorite_rounded;
      case ChartType.composite:
        return Icons.merge_type_rounded;
      case ChartType.davison:
        return Icons.compare_arrows_rounded;
      case ChartType.marks:
        return Icons.psychology_rounded;
      case ChartType.secondaryProgression:
        return Icons.trending_up_rounded;
      case ChartType.tertiaryProgression:
        return Icons.show_chart_rounded;
      case ChartType.solarReturn:
        return Icons.wb_sunny_rounded;
      case ChartType.lunarReturn:
        return Icons.nightlight_round_rounded;
      case ChartType.eclipse:
        return Icons.brightness_2_rounded;
      case ChartType.equinoxSolstice:
        return Icons.nature_rounded;
      case ChartType.firdaria:
        return Icons.hourglass_full_rounded;
      case ChartType.profection:
        return Icons.rotate_right_rounded;
      case ChartType.mundane:
        return Icons.public_rounded;
      case ChartType.horary:
        return Icons.help_outline_rounded;
      case ChartType.event:
        return Icons.event_rounded;
      default:
        return Icons.auto_awesome_rounded;
    }
  }

  /// 獲取星盤類型分類顏色
  Color _getChartTypeCategoryColor(ChartType chartType) {
    if (chartType == ChartType.natal) {
      return Colors.blue;
    } else if (chartType.isRelationshipChart) {
      return Colors.pink;
    } else if (chartType.isPredictiveChart) {
      return Colors.purple;
    } else if (chartType.isReturnChart) {
      return Colors.orange;
    } else if (chartType.isEventChart) {
      return Colors.green;
    } else if (chartType.isSpecialChart) {
      return Colors.teal;
    } else {
      return AppColors.royalIndigo;
    }
  }

  /// 獲取顯示標題（根據用戶模式）
  String _getDisplayTitle(ChartType chartType) {
    if (_userMode == 'starlight') {
      return _getStarlightTitle(chartType);
    }
    return chartType.displayName;
  }

  /// 獲取顯示描述（根據用戶模式）
  String _getDisplayDescription(ChartType chartType) {
    if (_userMode == 'starlight') {
      return _getStarlightDescription(chartType);
    }
    return _getChartTypeDescription(chartType);
  }

  /// 獲取初心者模式標題
  String _getStarlightTitle(ChartType chartType) {
    switch (chartType) {
      case ChartType.natal:
        return '我的性格分析';
      case ChartType.transit:
        return '當前運勢';
      case ChartType.synastry:
        return '配對分析';
      case ChartType.composite:
        return '關係深度分析';
      case ChartType.davison:
        return '關係時空分析';
      case ChartType.marks:
        return '內心感受分析';
      case ChartType.secondaryProgression:
        return '人生發展趨勢';
      case ChartType.tertiaryProgression:
        return '短期心理變化';
      case ChartType.solarReturn:
        return '年度運勢';
      case ChartType.lunarReturn:
        return '月度情緒週期';
      case ChartType.eclipse:
        return '重要天象影響';
      case ChartType.equinoxSolstice:
        return '季節能量分析';
      case ChartType.firdaria:
        return '古典時序分析';
      case ChartType.profection:
        return '年度預測分析';
      case ChartType.mundane:
        return '社會事件分析';
      case ChartType.horary:
        return '問題解答分析';
      case ChartType.event:
        return '事件時機分析';
      default:
        return '個人分析';
    }
  }

  /// 獲取初心者模式描述
  String _getStarlightDescription(ChartType chartType) {
    switch (chartType) {
      case ChartType.natal:
        return '了解您的個性特質、優缺點和天賦能力';
      case ChartType.transit:
        return '分析目前的運勢和需要注意的事項';
      case ChartType.synastry:
        return '分析兩人的相處模式和關係相容性';
      case ChartType.composite:
        return '了解兩人在一起會產生什麼樣的化學反應';
      case ChartType.davison:
        return '分析兩人相遇的時空意義和共同命運';
      case ChartType.marks:
        return '了解您在這段關係中的內心感受';
      case ChartType.secondaryProgression:
        return '分析您未來的人生發展方向和重要轉折';
      case ChartType.tertiaryProgression:
        return '了解您近期的心理狀態和情緒變化';
      case ChartType.solarReturn:
        return '分析這一年的整體運勢和發展重點';
      case ChartType.lunarReturn:
        return '了解每個月的情緒變化和內在需求';
      case ChartType.eclipse:
        return '分析日蝕月蝕等特殊天象對您的影響';
      case ChartType.equinoxSolstice:
        return '了解春夏秋冬四季對您的影響';
      case ChartType.firdaria:
        return '古典占星的時序分析技術';
      case ChartType.profection:
        return '古典占星的年度預測方法';
      case ChartType.mundane:
        return '分析社會大事件的占星影響';
      case ChartType.horary:
        return '針對具體問題的占星解答';
      case ChartType.event:
        return '分析特定事件的最佳時機';
      default:
        return '透過出生時間了解自己和他人';
    }
  }

  /// 獲取星盤類型描述
  String _getChartTypeDescription(ChartType chartType) {
    switch (chartType) {
      case ChartType.natal:
        return '個人出生星盤，分析性格與命運';
      case ChartType.transit:
        return '流年星盤，預測未來趨勢';
      case ChartType.synastry:
        return '合盤分析，探索關係相容性';
      case ChartType.composite:
        return '組合盤，分析關係本質';
      case ChartType.davison:
        return '戴維森盤，關係的中點分析';
      case ChartType.marks:
        return '馬克思盤，關係中的主觀感受';
      case ChartType.secondaryProgression:
        return '次限推運，內在心理成長';
      case ChartType.tertiaryProgression:
        return '三限推運，短期心理變化';
      case ChartType.solarReturn:
        return '太陽回歸盤，年度運勢預測';
      case ChartType.lunarReturn:
        return '月亮回歸盤，月度情緒週期';
      case ChartType.eclipse:
        return '日月蝕盤，重大轉折分析';
      case ChartType.equinoxSolstice:
        return '分至點盤，季節能量分析';
      case ChartType.firdaria:
        return '法達盤，古典時序技術';
      case ChartType.profection:
        return '小限法，古典年度預測';
      case ChartType.mundane:
        return '世俗占星，分析社會事件';
      case ChartType.horary:
        return '卜卦占星，回答具體問題';
      case ChartType.event:
        return '事件占星，特定事件分析';
      default:
        return '星盤分析工具';
    }
  }

  /// 導航到星盤頁面
  void _navigateToChartPage() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (_) =>
              ChartViewModel.withChartData(initialChartData: widget.chartData),
          child: ChartPage(chartData: widget.chartData),
        ),
      ),
    );
  }

  /// 導航到解讀紀錄頁面
  void _navigateToRecords() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const InterpretationRecordsPage(),
      ),
    );
  }

  /// 判斷是否應該顯示評分區域
  bool _shouldShowRatingSection() {
    // 傳統模式：檢查 interpretation 和 aiApiResponse
    if (!_useStreamingMode) {
      return (_interpretation?.isNotEmpty ?? false) &&
          (aiApiResponse?.success == true);
    }

    // 流式模式：檢查流式響應是否完成
    if (_useStreamingMode && _streamingResponse != null) {
      return _isStreamingComplete || (_streamingContent.isNotEmpty);
    }

    return false;
  }

  /// 構建評價區域
  Widget _buildRatingSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 標題區域
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.amber.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.star_rate,
                  color: Colors.amber,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Text(
                          '分析滿意度評分',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textDark,
                          ),
                        ),
                        const Spacer(), // 撐開兩端
                        if (_rating > 0) ...[
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.amber.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '$_rating/5',
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: Colors.amber,
                              ),
                            ),
                          ),
                        ]
                      ],
                    ),
                    const Text(
                      '您的回饋幫助我們提供更好的服務',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // 星級評分（5星制）
          Wrap(
            crossAxisAlignment: WrapCrossAlignment.center,
            spacing: 4,
            runSpacing: 8,
            children: [
              const Text(
                '評分：',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textDark,
                ),
              ),
              ...List.generate(5, (index) {
                return GestureDetector(
                  onTap: _hasRated
                      ? null
                      : () {
                          setState(() {
                            _rating = index + 1;
                          });
                        },
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    child: Icon(
                      index < _rating ? Icons.star : Icons.star_border,
                      color: _hasRated ? Colors.grey : Colors.amber,
                      size: 32,
                    ),
                  ),
                );
              }),
            ],
          ),

          const SizedBox(height: 16),

          // 快速回饋選項
          if (!_hasRated) ...[
            Row(
              children: [
                const Text(
                  '快速回饋：',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textDark,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: AppColors.royalIndigo.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Text(
                    '可多選',
                    style: TextStyle(
                      fontSize: 11,
                      color: AppColors.royalIndigo,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (_selectedQuickOptions.isNotEmpty) ...[
                  const SizedBox(width: 8),
                  Text(
                    '已選 ${_selectedQuickOptions.length} 項',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _buildQuickFeedbackOptions(),
            ),
            const SizedBox(height: 16),
          ],

          // 評論輸入
          TextField(
            controller: _commentController,
            enabled: !_hasRated,
            maxLines: 3,
            maxLength: 200,
            decoration: InputDecoration(
              hintText: '請分享您對這次分析的想法和建議...',
              hintStyle: TextStyle(
                color: Colors.grey[400],
                fontSize: 14,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Colors.grey),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide:
                    BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide:
                    const BorderSide(color: AppColors.royalIndigo, width: 2),
              ),
              contentPadding: const EdgeInsets.all(16),
              filled: true,
              fillColor: _hasRated
                  ? Colors.grey[100]
                  : Colors.grey.withValues(alpha: 0.05),
            ),
            onChanged: (value) {
              setState(() {
                _comment = value;
                // 當用戶手動編輯時，同步更新選項狀態
                _syncSelectedOptionsFromComment(value);
              });
            },
          ),

          const SizedBox(height: 16),

          // 公開/匿名切換
          if (!_hasRated) ...[
            Row(
              children: [
                Switch(
                  value: _isPublicRating,
                  onChanged: (value) {
                    setState(() {
                      _isPublicRating = value;
                    });
                  },
                  activeColor: AppColors.royalIndigo,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _isPublicRating ? '公開評價' : '匿名評價',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppColors.textDark,
                        ),
                      ),
                      Text(
                        _isPublicRating
                            ? '您的評價將顯示暱稱，幫助其他用戶參考'
                            : '您的評價將保持匿名，僅供內部改進參考',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
          ],

          // 提交按鈕或已評價狀態
          if (_hasRated) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green[600], size: 20),
                  const SizedBox(width: 8),
                  Text(
                    '感謝您的評價！',
                    style: TextStyle(
                      color: Colors.green[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ] else ...[
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed:
                    _rating > 0 && !_isSubmittingRating ? _submitRating : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.royalIndigo,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isSubmittingRating
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        '提交評價',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 載入已有評價
  Future<void> _loadExistingRating() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      // 生成唯一的評價ID（基於用戶、分析內容等）
      final ratingId = _generateRatingId();

      final querySnapshot = await FirebaseFirestore.instance
          .collection('analysis_ratings')
          .where('userId', isEqualTo: user.uid)
          .where('ratingId', isEqualTo: ratingId)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        final data = querySnapshot.docs.first.data();
        setState(() {
          _rating = data['rating'] ?? 0;
          _comment = data['comment'] ?? '';
          _commentController.text = _comment;
          _isPublicRating = data['isPublic'] ?? false;
          _hasRated = true;
        });
      }
    } catch (e) {
      logger.e('載入評價失敗 $e');
    }
  }

  /// 生成評價ID
  String _generateRatingId() {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return '';

    // 基於用戶ID、分析標題、副標題、關鍵點生成唯一ID
    final content =
        '${user.uid}_${widget.interpretationTitle}_${widget.subtitle}_${widget.keyPoint}';
    return content.hashCode.toString();
  }

  /// 提交評價
  Future<void> _submitRating() async {
    if (_rating == 0) {
      _showSnackBar('請選擇評分');
      return;
    }

    if (_comment.trim().isEmpty) {
      _showSnackBar('請輸入評價內容');
      return;
    }

    setState(() {
      _isSubmittingRating = true;
    });

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        _showSnackBar('請先登入');
        return;
      }

      // 獲取用戶暱稱
      final userDisplayName = await _getUserDisplayName(user.uid);
      final ratingId = _generateRatingId();
      final prefs = await SharedPreferences.getInstance();
      final userMode = prefs.getString('user_mode') ?? 'starmaster';
      final analysisMethod = prefs.getString('analysis_method') ?? 'system';

      logger.i("解讀模式：$userMode");
      logger.i("分析方式：$analysisMethod");

      // 準備評價數據
      final ratingData = {
        'userId': user.uid,
        'userEmail': user.email,
        'userName': userDisplayName,
        'ratingId': ratingId,
        'userMode': userMode,
        'analysisMethod': analysisMethod,
        'interpretationTitle': widget.interpretationTitle,
        'subtitle': widget.subtitle,
        'keyPoint': widget.keyPoint,
        'chartType': widget.chartData.chartType.displayName,
        'primaryPersonDateTime': widget.chartData.primaryPerson.dateTime,
        'primaryPersonBirthPlace': widget.chartData.primaryPerson.birthPlace,
        'rating': _rating,
        'comment': _comment.trim(),
        'isPublic': _isPublicRating,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'analysisContent': (_interpretation?.length ?? 0) > 100
            ? _interpretation?.substring(0, 100) ?? ''
            : _interpretation ?? '',
      };

      // 只保存到 analysis_ratings 集合（統一評價系統）
      await FirebaseFirestore.instance
          .collection('analysis_ratings')
          .add(ratingData);

      if (mounted) {
        setState(() {
          _hasRated = true;
          _isSubmittingRating = false;
        });

        _showSnackBar('評價提交成功，感謝您的反饋！');
      }
    } catch (e) {
      logger.e('提交評價失敗 $e');
      setState(() {
        _isSubmittingRating = false;
      });
      _showSnackBar('評價提交失敗，請稍後再試');
    }
  }

  /// 獲取用戶顯示名稱
  Future<String> _getUserDisplayName(String userId) async {
    try {
      // 首先嘗試從 user_profiles 獲取
      final userProfileDoc = await FirebaseFirestore.instance
          .collection(FirebaseCollections.userProfiles)
          .doc(userId)
          .get();

      if (userProfileDoc.exists) {
        final data = userProfileDoc.data()!;
        final displayName = data['display_name'] ?? data['displayName'];
        if (displayName != null && displayName.toString().isNotEmpty) {
          return displayName.toString();
        }
      }

      // 如果沒有，使用 Firebase Auth 的 displayName
      final user = FirebaseAuth.instance.currentUser;
      if (user?.displayName != null && user!.displayName!.isNotEmpty) {
        return user.displayName!;
      }

      // 最後使用匿名用戶
      return '匿名用戶';
    } catch (e) {
      logger.e('獲取用戶顯示名稱失敗 $e');
      return '匿名用戶';
    }
  }

  /// 顯示 SnackBar
  void _showSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.royalIndigo,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  /// 移除 Markdown 格式，方便分享給其他人
  String _removeMarkdownFormatting(String content) {
    // 移除 Markdown 標記，但保留內容
    String cleaned = content;

    // 先移除代碼塊（包含內容）
    final lines = cleaned.split('\n');
    final filteredLines = <String>[];
    bool inCodeBlock = false;

    for (final line in lines) {
      if (line.trim().startsWith('```')) {
        inCodeBlock = !inCodeBlock;
        continue; // 跳過代碼塊標記行
      }
      if (!inCodeBlock) {
        filteredLines.add(line);
      }
    }

    cleaned = filteredLines.join('\n');

    // 處理粗體：**text** -> text
    cleaned = cleaned.replaceAllMapped(
        RegExp(r'\*\*(.*?)\*\*'), (match) => match.group(1) ?? '');

    // 處理斜體：*text* -> text
    cleaned = cleaned.replaceAllMapped(
        RegExp(r'\*(.*?)\*'), (match) => match.group(1) ?? '');

    // 移除標題：# -> 空
    cleaned = cleaned.replaceAll(RegExp(r'#{1,6}\s*'), '');

    // 處理連結：[text](url) -> text
    cleaned = cleaned.replaceAllMapped(
        RegExp(r'\[([^\]]+)\]\([^)]+\)'), (match) => match.group(1) ?? '');

    // 處理行內代碼：`code` -> code
    cleaned = cleaned.replaceAllMapped(
        RegExp(r'`([^`]+)`'), (match) => match.group(1) ?? '');

    // 列表項目：- item -> • item
    cleaned =
        cleaned.replaceAll(RegExp(r'^\s*[-*+]\s+', multiLine: true), '• ');

    // 數字列表：1. item -> item
    cleaned = cleaned.replaceAll(RegExp(r'^\s*\d+\.\s+', multiLine: true), '');

    // 多餘的換行：最多保留兩個
    cleaned = cleaned.replaceAll(RegExp(r'\n{3,}'), '\n\n');

    return cleaned.trim();
  }

  /// 顯示占星注意事項提示視窗
  Future<void> _showAstrologyNoticeDialog() async {
    if (!mounted) return;

    final result = await AstrologyNoticeDialog.show(
      context,
      showModeToggle: true,
      showAnalysisMethodToggle: true,
      showVideoAndReviews: true,
    );

    if (result == true) {
      // 用戶點擊確認，繼續執行解讀
      _checkPaymentAndProceed();
    } else {
      // 用戶點擊取消，返回上一頁
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  /// 構建出生時間資訊行（包含不確定標記）
  Widget _buildTimeInfoRow(BirthData birthData) {
    final timeStr =
        '${birthData.dateTime.hour.toString().padLeft(2, '0')}:${birthData.dateTime.minute.toString().padLeft(2, '0')}';

    return Row(
      children: [
        Expanded(
          child: _buildInfoRow('出生時間', timeStr),
        ),
        if (birthData.isTimeUncertain) ...[
          const SizedBox(width: 8),
          GestureDetector(
            onTap: () => _showTimeUncertaintyDialog(),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.help_outline,
                size: 16,
                color: Colors.orange,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// 構建出生時間不確定警告
  Widget _buildTimeUncertaintyWarning() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.orange.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.warning_amber_rounded,
            color: Colors.orange,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '出生時間不確定，分析結果可能受到影響',
              style: TextStyle(
                color: Colors.orange[800],
                fontSize: 14,
              ),
            ),
          ),
          GestureDetector(
            onTap: () => _showTimeUncertaintyDialog(),
            child: Container(
              padding: const EdgeInsets.all(4),
              child: const Icon(
                Icons.help_outline,
                size: 16,
                color: Colors.orange,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 檢查是否有時間不確定的情況
  bool _hasTimeUncertainty() {
    return widget.chartData.primaryPerson.isTimeUncertain ||
        (widget.chartData.secondaryPerson?.isTimeUncertain ?? false);
  }

  /// 構建星盤時間不確定警告
  Widget _buildChartTimeUncertaintyWarning() {
    final primaryUncertain = widget.chartData.primaryPerson.isTimeUncertain;
    final secondaryUncertain =
        widget.chartData.secondaryPerson?.isTimeUncertain ?? false;

    String warningText;
    if (primaryUncertain && secondaryUncertain) {
      warningText = '兩位人物的出生時間都不確定，分析結果可能受到影響';
    } else if (primaryUncertain) {
      warningText = '出生時間不確定，分析結果可能受到影響';
    } else {
      warningText = '次要人物的出生時間不確定，分析結果可能受到影響';
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.orange.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.warning_amber_rounded,
            color: Colors.orange,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              warningText,
              style: TextStyle(
                color: Colors.orange[800],
                fontSize: 14,
              ),
            ),
          ),
          GestureDetector(
            onTap: () => _showTimeUncertaintyDialog(),
            child: Container(
              padding: const EdgeInsets.all(4),
              child: const Icon(
                Icons.help_outline,
                size: 16,
                color: Colors.orange,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 顯示出生時間不確定說明對話框
  Future<void> _showTimeUncertaintyDialog() async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(
                Icons.warning_amber_rounded,
                color: Colors.orange,
                size: 24,
              ),
              SizedBox(width: 8),
              Text('出生時間不確定的影響'),
            ],
          ),
          content: const SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '當出生時間不確定時，以下分析項目可能會受到影響：',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                SizedBox(height: 12),
                Text('• 上升星座（需要精確的出生時間）'),
                Text('• 宮位分析（宮位邊界會因時間而變化）'),
                Text('• 月亮星座（如果時間差距較大）'),
                Text('• 行星宮位位置'),
                Text('• 相位的精確度'),
                SizedBox(height: 12),
                Text(
                  '建議：',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                SizedBox(height: 8),
                Text('• 盡量確認準確的出生時間'),
                Text('• 重點關注太陽星座和主要行星位置'),
                Text('• 參考分析時保持適當的彈性'),
                // SizedBox(height: 12),
                // Text(
                //   '注意：分析已自動調整，會避免過度依賴時間相關的因素。',
                //   style: TextStyle(
                //     color: Colors.grey,
                //     fontSize: 14,
                //   ),
                // ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('了解'),
            ),
          ],
        );
      },
    );
  }

  /// 構建快速回饋選項
  List<Widget> _buildQuickFeedbackOptions() {
    final quickOptions = [
      // ✅ 分析準確度與契合感
      '分析很準確，很有幫助！',
      '完全說中我當下的感受',
      '與我目前的處境高度契合',
      '分析細膩，連細節都有涵蓋到',

      // 🧠 學習與啟發
      '內容豐富，學到很多',
      '解釋清楚易懂',
      '幫助我釐清了內在的卡點',
      '給了我新的思考角度',
      '讓我對星盤有更深的理解',

      // 🎯 實用性與建議
      '希望能更詳細一些',
      // '建議增加更多實用建議',
      // '如果能整理成個人占星報告會很實用',
      // '希望能附上具體的行動建議',
      // '建議搭配時事或流年提醒更有感',

      // 🗣️ 互動體驗與功能建議
      // '希望能有更多互動',
      // '期待能加入提問或討論的機會',
      // '如果能針對我選擇的主題再深入就更棒了！',
      // '希望能有語音或圖像說明',

      // 😄 情感共鳴與語氣風格
      '整體體驗很好',
      '感覺被理解了，謝謝！',
      // '語氣親切，讓人很安心',
      // '像是和懂我的朋友聊天',
      '分析角度很獨特',
    ];

    return quickOptions.map((option) {
      final isSelected = _selectedQuickOptions.contains(option);
      return GestureDetector(
        onTap: () {
          setState(() {
            if (isSelected) {
              // 如果已選中，則取消選擇
              _selectedQuickOptions.remove(option);
            } else {
              // 選擇新選項
              _selectedQuickOptions.add(option);
            }
            // 更新文字框內容
            _updateCommentFromSelectedOptions();
          });
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: isSelected
                ? AppColors.royalIndigo.withValues(alpha: 0.1)
                : Colors.grey.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isSelected
                  ? AppColors.royalIndigo
                  : Colors.grey.withValues(alpha: 0.3),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (isSelected) ...[
                Icon(
                  Icons.check_circle,
                  size: 16,
                  color: AppColors.royalIndigo,
                ),
                const SizedBox(width: 4),
              ],
              Flexible(
                child: Text(
                  option,
                  style: TextStyle(
                    fontSize: 13,
                    color:
                        isSelected ? AppColors.royalIndigo : AppColors.textDark,
                    fontWeight:
                        isSelected ? FontWeight.w500 : FontWeight.normal,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }).toList();
  }

  /// 根據選中的快速選項更新評論內容
  void _updateCommentFromSelectedOptions() {
    if (_selectedQuickOptions.isEmpty) {
      _comment = '';
      _commentController.text = '';
    } else {
      // 將選中的選項用分號連接
      final selectedText = _selectedQuickOptions.join('；');
      _comment = selectedText;
      _commentController.text = selectedText;
    }
  }

  /// 根據評論內容同步選項狀態
  void _syncSelectedOptionsFromComment(String comment) {
    final quickOptions = [
      '分析很準確，很有幫助！',
      '內容豐富，學到很多',
      '解釋清楚易懂',
      '給了我新的思考角度',
      '希望能更詳細一些',
      '建議增加更多實用建議',
      '分析角度很獨特',
      '對我的問題很有針對性',
      '希望能有更多互動',
      '整體體驗很好',
    ];

    _selectedQuickOptions.clear();

    // 檢查評論中包含哪些預設選項
    for (final option in quickOptions) {
      if (comment.contains(option)) {
        _selectedQuickOptions.add(option);
      }
    }
  }
}
